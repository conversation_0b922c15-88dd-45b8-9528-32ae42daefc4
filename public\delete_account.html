<!DOCTYPE html>
<html>
  <head>
    <title>Delete Account | Alien AI</title>
    <link
      href="https://cdn.jsdelivr.net/npm/daisyui@5.0.0-beta.8/daisyui.css"
      rel="stylesheet"
      type="text/css"
    />
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <style>
      .gsi-material-button {
        -moz-user-select: none;
        -webkit-user-select: none;
        -ms-user-select: none;
        -webkit-appearance: none;
        background-color: WHITE;
        background-image: none;
        border: 1px solid #747775;
        -webkit-border-radius: 4px;
        border-radius: 4px;
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
        color: #1f1f1f;
        cursor: pointer;
        font-family: "Roboto", arial, sans-serif;
        font-size: 14px;
        height: 40px;
        letter-spacing: 0.25px;
        outline: none;
        overflow: hidden;
        padding: 0 12px;
        position: relative;
        text-align: center;
        -webkit-transition:
          background-color 0.218s,
          border-color 0.218s,
          box-shadow 0.218s;
        transition:
          background-color 0.218s,
          border-color 0.218s,
          box-shadow 0.218s;
        vertical-align: middle;
        white-space: nowrap;
        width: auto;
        max-width: 400px;
        min-width: min-content;
      }

      .gsi-material-button .gsi-material-button-icon {
        height: 20px;
        margin-right: 12px;
        min-width: 20px;
        width: 20px;
      }

      .gsi-material-button .gsi-material-button-content-wrapper {
        -webkit-align-items: center;
        align-items: center;
        display: flex;
        -webkit-flex-direction: row;
        flex-direction: row;
        -webkit-flex-wrap: nowrap;
        flex-wrap: nowrap;
        height: 100%;
        justify-content: space-between;
        position: relative;
        width: 100%;
      }

      .gsi-material-button .gsi-material-button-contents {
        -webkit-flex-grow: 1;
        flex-grow: 1;
        font-family: "Roboto", arial, sans-serif;
        font-weight: 500;
        overflow: hidden;
        text-overflow: ellipsis;
        vertical-align: top;
      }

      .gsi-material-button .gsi-material-button-state {
        -webkit-transition: opacity 0.218s;
        transition: opacity 0.218s;
        bottom: 0;
        left: 0;
        opacity: 0;
        position: absolute;
        right: 0;
        top: 0;
      }

      .gsi-material-button:disabled {
        cursor: default;
        background-color: #ffffff61;
        border-color: #1f1f1f1f;
      }

      .gsi-material-button:disabled .gsi-material-button-contents {
        opacity: 38%;
      }

      .gsi-material-button:disabled .gsi-material-button-icon {
        opacity: 38%;
      }

      .gsi-material-button:not(:disabled):active .gsi-material-button-state,
      .gsi-material-button:not(:disabled):focus .gsi-material-button-state {
        background-color: #303030;
        opacity: 12%;
      }

      .gsi-material-button:not(:disabled):hover {
        -webkit-box-shadow:
          0 1px 2px 0 rgba(60, 64, 67, 0.30),
          0 1px 3px 1px rgba(60, 64, 67, 0.15);
        box-shadow:
          0 1px 2px 0 rgba(60, 64, 67, 0.30),
          0 1px 3px 1px rgba(60, 64, 67, 0.15);
      }

      .gsi-material-button:not(:disabled):hover .gsi-material-button-state {
        background-color: #303030;
        opacity: 8%;
      }
    </style>
  </head>
  <body>
    <div class="flex flex-col items-center mt-20 mb-10">
      <div class="flex flex-col items-center" style="width: 400px">
        <!-- LOGO -->
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="200"
          height="200"
          viewBox="0 0 24 24"
        >
          <path
            fill="currentColor"
            d="M12 3c4.97 0 9 3.58 9 8s-6 10-9 10s-9-5.58-9-10s4.03-8 9-8m-1.69 7.93C9.29 9.29 7.47 8.58 6.25 9.34s-1.38 2.71-.36 4.35c1.03 1.64 2.85 2.35 4.07 1.59c1.22-.78 1.37-2.71.35-4.35m3.38 0c-1.02 1.64-.87 3.57.35 4.35c1.22.76 3.04.05 4.07-1.59c1.02-1.64.86-3.59-.36-4.35s-3.04-.05-4.06 1.59M12 17.75c-2 0-2.5-.75-2.5-.75c0 .03.5 2 2.5 2s2.5-2 2.5-2s-.5.75-2.5.75"
          />
        </svg>
        <div class="font font-bold text-2xl">Alien AI</div>

        <div class="divider">Delete Account</div>

        <div id="delete_log_in" class="flex flex-col items-center">
          <div>
            Please <strong>Sign In</strong> before deleting your account
          </div>

          <div class="divider"></div>
          <button class="gsi-material-button" onclick="signIn()">
            <div class="gsi-material-button-state"></div>
            <div class="gsi-material-button-content-wrapper">
              <div class="gsi-material-button-icon">
                <svg
                  version="1.1"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 48 48"
                  xmlns:xlink="http://www.w3.org/1999/xlink"
                  style="display: block"
                >
                  <path
                    fill="#EA4335"
                    d="M24 9.5c3.54 0 6.71 1.22 9.21 3.6l6.85-6.85C35.9 2.38 30.47 0 24 0 14.62 0 6.51 5.38 2.56 13.22l7.98 6.19C12.43 13.72 17.74 9.5 24 9.5z"
                  >
                  </path>
                  <path
                    fill="#4285F4"
                    d="M46.98 24.55c0-1.57-.15-3.09-.38-4.55H24v9.02h12.94c-.58 2.96-2.26 5.48-4.78 7.18l7.73 6c4.51-4.18 7.09-10.36 7.09-17.65z"
                  >
                  </path>
                  <path
                    fill="#FBBC05"
                    d="M10.53 28.59c-.48-1.45-.76-2.99-.76-4.59s.27-3.14.76-4.59l-7.98-6.19C.92 16.46 0 20.12 0 24c0 3.88.92 7.54 2.56 10.78l7.97-6.19z"
                  >
                  </path>
                  <path
                    fill="#34A853"
                    d="M24 48c6.48 0 11.93-2.13 15.89-5.81l-7.73-6c-2.15 1.45-4.92 2.3-8.16 2.3-6.26 0-11.57-4.22-13.47-9.91l-7.98 6.19C6.51 42.62 14.62 48 24 48z"
                  >
                  </path>
                  <path fill="none" d="M0 0h48v48H0z"></path>
                </svg>
              </div>
              <span class="gsi-material-button-contents"
              >Sign in with Google</span>
              <span style="display: none">Sign in with Google</span>
            </div>
          </button>
        </div>
      </div>

      <div id="delete_confirmation" class="flex flex-col items-center hidden">
        <div class="font-bold">Are you sure want to delete your account?</div>

        <div class="text-sm flex flex-col items-center">
          <div>Your account will be deleted within 30 days.</div>
          <div>Account deletion will be canceled if you Sign in again.</div>
        </div>
        <div class="divider"></div>
        <div class="btn btn-primary" onclick="confirmDelete()">
          Confirm Delete Account
        </div>
      </div>

      <div id="delete_confirm" class="flex flex-col items-center hidden">
        <div class="font-bold">Your account will be deleted!</div>
        <div class="divider"></div>
      </div>
    </div>
  </body>

  <script type="module">
    // Import the functions you need from the SDKs you need
    import { initializeApp } from "https://www.gstatic.com/firebasejs/11.3.1/firebase-app.js";
    import {
      getAuth,
      GoogleAuthProvider,
      signInWithPopup,
    } from "https://www.gstatic.com/firebasejs/11.3.1/firebase-auth.js";
    // To Add SDKs for Firebase products that you want to use
    // https://firebase.google.com/docs/web/setup#available-libraries

    // Your web app's Firebase configuration
    // For Firebase JS SDK v7.20.0 and later, measurementId is optional
    const firebaseConfig = {
      apiKey: "AIzaSyCqNtcTIwLtGbuJPNSwo8bga7pVHzSwFK4",
      authDomain: "alienai-id.firebaseapp.com",
      projectId: "alienai-id",
      storageBucket: "alienai-id.firebasestorage.app",
      messagingSenderId: "************",
      appId: "1:************:web:ad4e10d7a52f956bd367b4",
      measurementId: "G-916D58SQVG",
    };

    // Initialize Firebase
    const provider = new GoogleAuthProvider();
    const app = initializeApp(firebaseConfig);
    const auth = getAuth();

    window.signIn = async () => {
      try {
        await signInWithPopup(auth, provider);
        document.getElementById("delete_log_in").classList.add("hidden");
        document.getElementById("delete_confirmation").classList.remove(
          "hidden",
        );
      } catch (ex) {
        console.log("ex", ex);
        alert("Sign in failed");
      }
    };

    window.confirmDelete = async () => {
      const currentUser = auth.currentUser;
      const idToken = await currentUser.getIdToken();

      try {
        await fetch("/_accout_delete", {
          method: "POST",
          body: {
            email: currentUser.email,
            idToken: idToken,
          },
        });

        document.getElementById("delete_log_in").classList.add("hidden");
        document.getElementById("delete_confirmation").classList.add(
          "hidden",
        );
        document.getElementById("delete_confirm").classList.remove(
          "hidden",
        );
      } catch (ex) {
        alert("Delete account failed");
        console.log("Delete account failed", ex);
      }
    };
  </script>
</html>
