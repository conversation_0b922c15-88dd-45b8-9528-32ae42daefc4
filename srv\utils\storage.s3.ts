// deno-lint-ignore-file no-async-promise-executor
import { Readable } from "node:stream";
import { Client, type ClientOptions } from "minio";
import { StorageBackend } from "./storage";

export interface StorageS3Options extends ClientOptions {
  bucket: string;
  prefix?: string;
}

export class StorageS3 extends StorageBackend {
  readonly client: Client;
  readonly bucketId: string;
  readonly prefix: string;

  constructor(opts: StorageS3Options) {
    super();
    this.bucketId = opts.bucket;
    this.prefix = opts.prefix ?? "";
    this.client = new Client(opts);
  }
  async getMeta(path: string) {
    return await this.client.statObject(this.bucketId, this.prefix + path);
  }

  async upload(path: string, mime: string, stream: Readable): Promise<void> {
    await this.client.putObject(
      this.bucketId,
      this.prefix + path,
      stream,
      undefined,
      {
        "Content-Type": mime,
      },
    );
  }
  async download(path: string): Promise<Readable> {
    return await this.client.getObject(this.bucketId, this.prefix + path);
  }
  async delete(path: string): Promise<void> {
    return await this.client.removeObject(this.bucketId, this.prefix + path);
  }
}
