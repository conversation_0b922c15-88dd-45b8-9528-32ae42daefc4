
import { existsSync } from 'fs';
// Import our local serveStatic middleware
import { serveStatic } from "./serve-static";
import { app } from '../instance';

// Use hardcoded path for public directory
const publicPath = process.cwd() + '/public';

// if (existsSync(publicPath)) {
//   console.log(`[srv.static] Contents of public directory:`);
//   const fs = require('fs');
//   fs.readdirSync(publicPath).forEach((file: string) => {
//     console.log(`  - ${file}`);
//   });
// }

// First try to serve the exact file
app.get(
    "*",
    serveStatic({
        root: publicPath,
        onNotFound(_p, _c) {
            console.log(`[srv.static] File not found: ${_p}`);
        },
    }),
);

// If file not found, serve index.html (for SPA routing)
app.get("*", serveStatic({ root: publicPath, path: "index.html" }));
