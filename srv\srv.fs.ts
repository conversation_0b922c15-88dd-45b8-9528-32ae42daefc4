// deno-lint-ignore-file no-async-promise-executor no-explicit-any require-awai
// import { Transform } from 'node:stream';

import type { Context } from "hono";
import { Readable } from "node:stream";
import { app, storage } from "../instance";

app.get("/_fs/*", async (c: Context) => {
    const [, , pid, hash] = c.req.path.split("/");

    // -------------------------------------------- cache
    const existing = c.req.header('If-None-Match');
    if (existing === hash) {
        console.log('get', pid, hash, '304');
        return new Response(null, {
            status: 304,
            headers: {
                'ETag': existing,
            },
        });
    }

    // -------------------------------------------- stream
    try {
        console.log('get', pid, hash, '200');
        const stream = await storage.download("sha512/" + hash);
        const resp = new Response(stream as any);
        resp.headers.set("Cache-Control", "public, max-age=31536000, immutable");
        resp.headers.set(
            "Expires",
            new Date(Date.now() + 31536000000).toUTCString(),
        ); // Approximately one year in the future
        resp.headers.set('ETag', hash);

        return resp;
    } catch (_) {
        return new Response("Not Found", { status: 404 });
    }
});

// ! TODO: download using public API,
app.put("/_fs/*", async (c: Context) => {
    const [, , pid, hash] = c.req.path.split("/");
    // l("posting", { aid, hash, path: c.req.path });

    // check if exists
    try {
        const meta = await storage.getMeta("sha512/" + hash);
        // l("file exists", meta);
        return new Response("OK", { status: 200 });
    } catch (_) {
        // l('file not exists);
    }

    // get content type
    const mime = c.req.header("Content-Type");
    // l('uploaded, mime', mime)

    const body = c.req.raw.body;
    if (body === null) throw new Error("Request cannot be empty");

    return new Promise(async (resolve) => {
        const nodeStream = new Readable({
            read() { }, // The read method is required
        });

        // Get a reader from the DOM stream
        const reader = body.getReader();

        storage.upload(
            "sha512/" + hash,
            mime ?? "application/octet-stream",
            nodeStream,
        ).then(() => {
            // l('done 2')
            resolve(new Response("OK"));
        });

        // Function to pump data from the DOM stream to the Node.js stream
        let chunk = await reader.read();
        while (!chunk.done) {
            nodeStream.push(chunk.value);
            // l('push', chunk.value)
            chunk = await reader.read();
        }
        // l('done')
        nodeStream.push(null);
    });
});
