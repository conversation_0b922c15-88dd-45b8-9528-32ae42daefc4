import { Hono } from 'hono';
import { StorageS3 } from './srv/utils/storage.s3';
import { S3_URL } from './config';

export const app = new Hono();

// ------------------------ Storage
const s3URL = new URL(S3_URL);
export const storage = new StorageS3({
    endPoint: s3URL.hostname,

    port: s3URL.port == "" ? undefined : parseInt(s3URL.port),
    accessKey: s3URL.username,
    secretKey: s3URL.password,
    bucket: s3URL.pathname.slice(1),
    prefix: "",
    useSSL: s3URL.protocol === "https:",
} as any);
