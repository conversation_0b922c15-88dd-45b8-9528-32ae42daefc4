<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Voice Recognition</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }

        #status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 5px;
            background-color: #e0e0e0;
        }

        #result {
            margin-top: 10px;
            min-height: 100px;
            padding: 10px;
            border-radius: 5px;
            background-color: white;
            border: 1px solid #ccc;
        }
    </style>
</head>

<body>
    <h1>Voice Recognition Debug</h1>
    <div id="status">Status: Idle</div>
    <div id="result"></div>

    <!-- Debug Controls -->
    <div style="margin-top: 20px; padding: 15px; background-color: #e9f7fe; border-radius: 5px;">
        <div id="rawData"
            style="margin-bottom: 15px; padding: 10px; background-color: #f5f5f5; border-radius: 4px; font-family: monospace; white-space: pre-wrap;">
            Raw data will appear here</div>
        <h3>Debug Controls</h3>
        <div>
            <button id="startBtn"
                style="padding: 10px; margin: 5px; background-color: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer;">Start
                Recognition</button>
            <button id="stopBtn"
                style="padding: 10px; margin: 5px; background-color: #f44336; color: white; border: none; border-radius: 4px; cursor: pointer;">Stop
                Recognition</button>
            <button id="testBtn"
                style="padding: 10px; margin: 5px; background-color: #2196F3; color: white; border: none; border-radius: 4px; cursor: pointer;">Test
                Indonesian Text</button>
            <button id="listLangBtn"
                style="padding: 10px; margin: 5px; background-color: #9C27B0; color: white; border: none; border-radius: 4px; cursor: pointer;">List
                Available Languages</button>
        </div>

        <div style="margin-top: 10px;">
            <label for="langSelect">Language:</label>
            <select id="langSelect" style="padding: 8px; margin: 5px; border-radius: 4px;">
                <option value="">Loading languages...</option>
            </select>
        </div>

        <div style="margin-top: 10px;">
            <h4>Environment Info:</h4>
            <div id="secureContextInfo"></div>
            <div id="browserInfo"></div>
            <div id="speechApiInfo"></div>
        </div>
    </div>

    <script>
        // Check if browser supports speech recognition
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;

        if (!SpeechRecognition) {
            document.getElementById('status').textContent = 'Status: Speech recognition not supported in this browser';
            sendMessageToFlutter({
                type: 'error',
                message: 'Speech recognition not supported in this browser'
            });
        }

        // Create recognition instance
        const recognition = SpeechRecognition ? new SpeechRecognition() : null;
        let isListening = false;

        if (recognition) {
            // Configure recognition
            recognition.continuous = false;
            recognition.interimResults = true;
            recognition.lang = 'id-ID'; // Set Indonesian as default language

            // Recognition events
            recognition.onstart = () => {
                isListening = true;
                document.getElementById('status').textContent = 'Status: Listening...';
                sendMessageToFlutter({
                    type: 'status',
                    status: 'listening'
                });
            };

            recognition.onresult = (event) => {
                let interimTranscript = '';
                let finalTranscript = '';

                for (let i = event.resultIndex; i < event.results.length; i++) {
                    const transcript = event.results[i][0].transcript;
                    const confidence = event.results[i][0].confidence;

                    if (event.results[i].isFinal) {
                        finalTranscript += transcript;
                        sendMessageToFlutter({
                            type: 'result',
                            result: transcript,
                            confidence: confidence,
                            isFinal: true
                        });
                    } else {
                        interimTranscript += transcript;
                        sendMessageToFlutter({
                            type: 'result',
                            result: transcript,
                            confidence: confidence,
                            isFinal: false
                        });
                    }
                }

                document.getElementById('result').innerHTML =
                    `<div>${finalTranscript}</div>
                     <div style="color: gray;">${interimTranscript}</div>`;
            };

            recognition.onerror = (event) => {
                document.getElementById('status').textContent = `Status: Error: ${event.error}`;
                sendMessageToFlutter({
                    type: 'error',
                    error: event.error
                });
            };

            recognition.onend = () => {
                isListening = false;
                document.getElementById('status').textContent = 'Status: Stopped';
                sendMessageToFlutter({
                    type: 'status',
                    status: 'stopped'
                });
            };
        }

        // Function to send messages to Flutter
        function sendMessageToFlutter(message) {
            if (window.flutter_inappwebview) {
                window.flutter_inappwebview.callHandler('messageHandler', message);
            }
        }

        // API functions that can be called from Flutter
        window.startRecognition = function (language) {
            if (!recognition) {
                sendMessageToFlutter({
                    type: 'error',
                    message: 'Speech recognition not supported'
                });
                return;
            }

            if (language) {
                recognition.lang = language;
            }

            if (!isListening) {
                try {
                    recognition.start();
                } catch (error) {
                    sendMessageToFlutter({
                        type: 'error',
                        message: error.message
                    });
                }
            }
        };

        window.stopRecognition = function () {
            if (recognition && isListening) {
                recognition.stop();
            }
        };

        window.listLanguages = function () {
            // Get the actual list of voices available on the system
            const voices = window.speechSynthesis.getVoices();
            const languageMap = new Map();

            // Log all available voices to console for debugging
            console.log('Available voices:');
            voices.forEach(voice => {
                console.log(`Name: ${voice.name}, Lang: ${voice.lang}, Default: ${voice.default}`);

                // Extract the language code (e.g., 'en-US' from the voice.lang)
                const langCode = voice.lang;

                // Only add each language code once (there might be multiple voices per language)
                if (!languageMap.has(langCode)) {
                    languageMap.set(langCode, {
                        code: langCode,
                        name: getLanguageName(langCode),
                        voiceName: voice.name
                    });
                }
            });

            // Convert the Map to an Array
            let languages = Array.from(languageMap.values());

            // If no voices were found, return an empty array instead of using a fallback list
            if (languages.length === 0) {
                console.log('No voices found, returning empty array');
                // No fallback list - just use the empty array
            }

            // Sort languages alphabetically by name
            languages.sort((a, b) => {
                // Put Indonesian first
                if (a.code === 'id-ID') return -1;
                if (b.code === 'id-ID') return 1;
                return a.name.localeCompare(b.name);
            });

            // Helper function to get a human-readable language name from a language code
            function getLanguageName(langCode) {
                // This is a simple mapping of common language codes to names
                const langNames = {
                    'id-ID': 'Indonesian',
                    'en-US': 'English (US)',
                    'en-GB': 'English (UK)',
                    'es-ES': 'Spanish (Spain)',
                    'es-MX': 'Spanish (Mexico)',
                    'fr-FR': 'French',
                    'de-DE': 'German',
                    'it-IT': 'Italian',
                    'pt-BR': 'Portuguese (Brazil)',
                    'ru-RU': 'Russian',
                    'zh-CN': 'Chinese (Simplified)',
                    'ja-JP': 'Japanese',
                    'ko-KR': 'Korean',
                    'ar-SA': 'Arabic',
                    'hi-IN': 'Hindi'
                };

                // Return the mapped name or the code itself if not found
                return langNames[langCode] || `Language (${langCode})`;
            }

            // Display the languages in the debug section
            if (languages.length === 0) {
                document.getElementById('rawData').innerHTML =
                    `No voices found on this system. Speech synthesis may not be fully supported.`;
            } else {
                document.getElementById('rawData').innerHTML =
                    `Found ${languages.length} languages:<br>` +
                    languages.map(lang => `${lang.code}: ${lang.name}${lang.voiceName ? ` (${lang.voiceName})` : ''}`).join('<br>');
            }

            // Send the languages to Flutter
            sendMessageToFlutter({
                type: 'languages',
                languages: languages
            });

            return languages;
        };

        // Initialize speechSynthesis to ensure voices are loaded
        if ('speechSynthesis' in window) {
            // In some browsers, getVoices() is asynchronous and needs an event listener
            if (window.speechSynthesis.onvoiceschanged !== undefined) {
                window.speechSynthesis.onvoiceschanged = function () {
                    console.log('Voices loaded');
                };
            }

            // Force loading of voices
            window.speechSynthesis.getVoices();
        } else {
            console.log('Speech synthesis not supported');
        }

        // Debug button event listeners
        document.getElementById('startBtn').addEventListener('click', () => {
            const language = document.getElementById('langSelect').value;
            window.startRecognition(language);
        });

        document.getElementById('stopBtn').addEventListener('click', () => {
            window.stopRecognition();
        });

        document.getElementById('testBtn').addEventListener('click', () => {
            // Test with some Indonesian text
            const testText = 'Halo, ini adalah teks bahasa Indonesia. Saya sedang menguji pengenalan suara.';

            // Display in raw data section
            document.getElementById('rawData').innerHTML =
                `Test text: "${testText}"<br>
                 Character codes: ${Array.from(testText).map(c => c.charCodeAt(0)).join(', ')}<br>
                 Length: ${testText.length} chars`;

            // Send to Flutter
            sendMessageToFlutter({
                type: 'result',
                result: testText,
                confidence: 1.0,
                isFinal: true
            });

            // Also display in result area
            document.getElementById('result').innerHTML =
                `<div>${testText}</div>
                 <div style="color: #666; font-size: 0.8em; margin-top: 10px;">Length: ${testText.length} chars</div>`;
        });

        document.getElementById('listLangBtn').addEventListener('click', () => {
            window.listLanguages();
        });

        // Display environment information
        function displayEnvironmentInfo() {
            // Secure context
            const isSecureContext = window.isSecureContext;
            document.getElementById('secureContextInfo').innerHTML =
                `Secure Context: <strong>${isSecureContext ? 'Yes ✅' : 'No ❌'}</strong>`;

            // Browser info
            document.getElementById('browserInfo').innerHTML =
                `Browser: <strong>${navigator.userAgent}</strong>`;

            // Speech API
            document.getElementById('speechApiInfo').innerHTML =
                `Speech Recognition API: <strong>${SpeechRecognition ? 'Available ✅' : 'Not Available ❌'}</strong>`;
        }

        // Function to populate language dropdown
        function populateLanguageDropdown() {
            const languages = window.listLanguages();
            const langSelect = document.getElementById('langSelect');

            // Clear existing options
            langSelect.innerHTML = '';

            // Add languages to dropdown
            languages.forEach(lang => {
                const option = document.createElement('option');
                option.value = lang.code;
                option.textContent = lang.name;

                // Set Indonesian as default selected language
                if (lang.code === 'id-ID') {
                    option.selected = true;
                }

                langSelect.appendChild(option);
            });

            // If no languages were found, add a default option
            if (languages.length === 0) {
                const option = document.createElement('option');
                option.value = 'id-ID';
                option.textContent = 'Indonesian';
                option.selected = true;
                langSelect.appendChild(option);
            }
        }

        // Notify Flutter that the page is loaded and ready
        window.addEventListener('load', () => {
            sendMessageToFlutter({
                type: 'status',
                status: 'ready'
            });

            // Display environment info
            displayEnvironmentInfo();

            // Populate language dropdown
            setTimeout(() => {
                populateLanguageDropdown();
            }, 400)
        });
    </script>
</body>

</html>