import type { Con<PERSON>, <PERSON><PERSON>Handler } from 'hono'
import { extname, join } from 'path'
import { readFile, stat } from 'fs/promises'
import { existsSync } from 'fs'

type ServeStaticOptions = {
  root?: string
  path?: string
  rewriteRequestPath?: (path: string) => string
  onNotFound?: (path: string, c: Context) => void
  mimes?: Record<string, string>
}

export const serveStatic = (options: ServeStaticOptions = { root: '' }): MiddlewareHandler => {
  return async (c, next) => {
    // Do nothing if Response is already set
    if (c.finalized) {
      await next()
      return
    }

    const url = new URL(c.req.url)
    let path = options.path ?? url.pathname

    if (options.rewriteRequestPath) {
      path = options.rewriteRequestPath(path)
    }

    // Use path.join for proper path handling
    const filePath = options.root ? join(options.root, path.startsWith('/') ? path.substring(1) : path) : path
    // console.log(`[serveStatic] Trying to serve: ${filePath}`)

    try {
      const fileStat = await stat(filePath)

      if (fileStat.isDirectory()) {
        console.log(`[serveStatic] ${filePath} is a directory, skipping`)
        await next()
        return
      }

    //   console.log(`[serveStatic] Found file: ${filePath}`)
      const fileContent = await readFile(filePath)
      const mimeType = getMimeType(path, options.mimes)

      return new Response(fileContent, {
        headers: {
          'Content-Type': mimeType,
        },
      })
    } catch (e: any) {
      console.log(`[serveStatic] Error serving ${filePath}:`, e.message || e)
      if (options.onNotFound) {
        options.onNotFound(path, c)
      }
      await next()
    }
  }
}

const getMimeType = (path: string, mimes: Record<string, string> = {}): string => {
  const extension = extname(path).substring(1).toLowerCase()

  const defaultMimes: Record<string, string> = {
    'txt': 'text/plain',
    'html': 'text/html',
    'css': 'text/css',
    'js': 'text/javascript',
    'json': 'application/json',
    'png': 'image/png',
    'jpg': 'image/jpeg',
    'jpeg': 'image/jpeg',
    'gif': 'image/gif',
    'svg': 'image/svg+xml',
    'ico': 'image/x-icon',
    'webp': 'image/webp',
    'woff': 'font/woff',
    'woff2': 'font/woff2',
    'ttf': 'font/ttf',
    'otf': 'font/otf',
    'mp4': 'video/mp4',
    'webm': 'video/webm',
    'mp3': 'audio/mpeg',
    'wav': 'audio/wav',
    'pdf': 'application/pdf',
    'xml': 'application/xml',
  }

  const mergedMimes = { ...defaultMimes, ...mimes }

  return mergedMimes[extension] || 'application/octet-stream'
}
