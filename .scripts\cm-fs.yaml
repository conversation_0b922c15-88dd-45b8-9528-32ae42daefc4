apiVersion: v1
kind: Namespace
metadata:
  name: cm-fs
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cm-fs
  namespace: cm-fs
  labels:
    app: cm-fs
spec:
  replicas: 2
  selector:
    matchLabels:
      app: cm-fs
  template:
    metadata:
      labels:
        app: cm-fs
    spec:
      containers:
        - name: cm-fs
          image: docker.alienai.id/chitosa/cm-fs:latest
          ports:
            - containerPort: 9495
          env:
            - name: S3_URL
              value: "http://WUQXPAPDWJXJQC94Z27Z:<EMAIL>:9000/alien"

          resources:
            limits:
              cpu: "1"
              memory: "256Mi"
            requests:
              cpu: "250m"
              memory: "128Mi"
---
apiVersion: v1
kind: Service
metadata:
  name: cm-fs
  namespace: cm-fs
spec:
  selector:
    app: cm-fs
  ports:
    - protocol: TCP
      port: 9495
      targetPort: 9495
  type: ClusterIP
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: cm-fs-ingress
  namespace: cm-fs
spec:
  ingressClassName: traefik
  rules:
  - host: "fs.alienai.id"
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: cm-fs
            port:
              number: 9495

