import { Readable } from "node:stream";

export abstract class StorageBackend {
  abstract getMeta(path: string): Promise<any>;
  abstract upload(path: string, mime: string, stream: Readable): Promise<void>;
  abstract download(path: string): Promise<Readable>;
  abstract delete(path: string): Promise<void>;

  async keep(path: string | undefined): Promise<string | undefined> {
    return undefined;
  }

  downloadAsBuffer(path: string): Promise<Buffer> {
    return new Promise(async (resolve, reject) => {
      const stream = await this.download(path);
      const buffers: Buffer[] = [];
      stream.on("data", (chunk) => {
        buffers.push(chunk);
      });

      stream.on("end", () => {
        resolve(Buffer.concat(buffers));
      });

      stream.on("error", (err) => {
        reject(err);
      });
    });
  }
}
