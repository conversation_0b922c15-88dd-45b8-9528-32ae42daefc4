<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Face Recognition</title>
  <script src="https://unpkg.com/face-api.js@0.22.2/dist/face-api.min.js"></script>
  <style>
    html, body {
      margin: 0;
      padding: 0;
      overflow: hidden;
      background: black;
    }
    #video, #overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      object-fit: cover;
    }
  </style>
</head>
<body>
  <video id="video" autoplay muted playsinline></video>
  <canvas id="overlay"></canvas>

  <script>
    const MODEL_URL = 'https://raw.githubusercontent.com/justadudewhohacks/face-api.js/master/weights';
    const video = document.getElementById('video');
    const canvas = document.getElementById('overlay');
    const ctx = canvas.getContext('2d');

    let previousDescriptors = [];
    let previousFaceCount = 0;
    const THRESHOLD = 0.5;

    async function loadModels() {
      await faceapi.nets.tinyFaceDetector.loadFromUri(MODEL_URL);
      await faceapi.nets.faceLandmark68Net.loadFromUri(MODEL_URL);
      await faceapi.nets.faceRecognitionNet.loadFromUri(MODEL_URL);
    }

    async function startVideo() {
      const stream = await navigator.mediaDevices.getUserMedia({ video: {} });
      video.srcObject = stream;
    }

    function resizeCanvas() {
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
    }

    function isFaceKnown(currentDesc, knownDescs, threshold = THRESHOLD) {
      return knownDescs.some((prevDesc) => {
        const dist = faceapi.euclideanDistance(currentDesc, prevDesc);
        return dist < threshold;
      });
    }

    async function processFrame() {
      const options = new faceapi.TinyFaceDetectorOptions();
      const detections = await faceapi
        .detectAllFaces(video, options)
        .withFaceLandmarks()
        .withFaceDescriptors();

      ctx.clearRect(0, 0, canvas.width, canvas.height);
      ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

      const resized = faceapi.resizeResults(detections, {
        width: canvas.width,
        height: canvas.height,
      });

      faceapi.draw.drawDetections(canvas, resized);
      faceapi.draw.drawFaceLandmarks(canvas, resized);

      const newDescriptors = detections.map(d => d.descriptor);
      const currentFaceCount = newDescriptors.length;

      let hasChanged = false;

      // 🔄 Case 1: No face detected now, but there were before
      if (currentFaceCount === 0 && previousFaceCount > 0) {
        console.log("🚫 No face detected");
        hasChanged = true;
      }

      // 🔄 Case 2: Face count changed
      if (currentFaceCount !== previousFaceCount) {
        console.log(`👥 Face count changed: ${previousFaceCount} → ${currentFaceCount}`);
        hasChanged = true;
      }

      // 🔄 Case 3: Unknown face appears
      for (const desc of newDescriptors) {
        if (!isFaceKnown(desc, previousDescriptors)) {
          console.log("🔍 New or changed face detected");
          hasChanged = true;
          break;
        }
      }

      if (hasChanged) {
        previousDescriptors = newDescriptors;
        previousFaceCount = currentFaceCount;
      }

      requestAnimationFrame(processFrame);
    }

    video.addEventListener('playing', () => {
      resizeCanvas();
      requestAnimationFrame(processFrame);
    });

    loadModels().then(startVideo);
  </script>
</body>
</html>
